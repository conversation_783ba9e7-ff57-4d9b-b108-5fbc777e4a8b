'use client';

import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { useState } from 'react';
import { useDeleteDriver } from '../api/mutations';
import { Driver, ListDriverResponse } from '../types/driver';
import { DeleteDriverDialog } from './delete-driver-dialog';
import { EditDriver } from './edit-driver';
import { DriverTableEmpty } from './driver-table-empty';
import { DriverTableFilteredEmpty } from './driver-table-filtered-empty';
import { DriverTableLoading } from './driver-table-loading';
import { CustomPagination } from '@/components/pagination';

// Define the columns for the table
const getColumns = ({
   deleteDriverMutation,
   handleDeleteClick,
   handleEditClick,
   handleViewClick,
   driverToDelete,
}: {
   handleDeleteClick: (id: number) => void;
   handleEditClick: (id: number) => void;
   handleViewClick: (id: number) => void;
   deleteDriverMutation: any;
   driverToDelete: number | null;
}): ColumnDef<Driver>[] => [
   {
      accessorKey: 'name',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Driver</div>,
      cell: ({ row }) => {
         const driver = row.original as Driver;
         return (
            <div className='text-left'>
               <div className='font-semibold text-sm'>
                  {driver.firstName} {driver.lastName}
               </div>
               <div className='text-xs text-gray-500'>{driver.driverId}</div>
            </div>
         );
      },
      size: 200,
   },
   {
      accessorKey: 'contact',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Contact</div>,
      cell: ({ row }) => {
         const driver = row.original as Driver;
         return (
            <div className='text-left'>
               <div className='text-sm'>{driver.phoneNumber}</div>
               <div className='text-xs text-gray-500'>{driver.email}</div>
            </div>
         );
      },
      size: 200,
   },
   {
      accessorKey: 'location',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Location</div>,
      cell: ({ row }) => {
         const driver = row.original as Driver;
         return (
            <div className='text-left'>
               <div className='text-sm'>{driver.location}</div>
            </div>
         );
      },
      size: 150,
   },
   {
      accessorKey: 'status',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Status</div>,
      cell: ({ row }) => {
         const status = row.getValue('status') as string;
         return (
            <div className='text-left'>
               {status === 'pending' ? (
                  <span className='inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-700'>
                     Pending
                  </span>
               ) : status === 'active' ? (
                  <span className='inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-700'>
                     Active
                  </span>
               ) : status === 'inactive' ? (
                  <span className='inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-700'>
                     Inactive
                  </span>
               ) : (
                  <span className='inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700'>
                     {status}
                  </span>
               )}
            </div>
         );
      },
      size: 120,
   },
   {
      accessorKey: 'progress',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Progress</div>,
      cell: ({ row }) => {
         const driver = row.original as Driver;
         const progress = driver.progress || 0;
         return (
            <div className='text-left'>
               <span className='text-sm'>{progress}%</span>
            </div>
         );
      },
      size: 120,
   },
   {
      accessorKey: 'joinedDate',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Joined</div>,
      cell: ({ row }) => {
         const joinedDate = row.getValue('joinedDate');
         if (!joinedDate) return <div className='text-left text-sm'>-</div>;

         const date = new Date(joinedDate as string);
         return (
            <div className='text-left'>
               <span className='text-sm'>
                  {date.toLocaleDateString('en-US', {
                     year: 'numeric',
                     month: '2-digit',
                     day: '2-digit',
                  })}
               </span>
            </div>
         );
      },
      size: 100,
   },
   {
      id: 'actions',
      header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Actions</div>,
      cell: ({ row }) => {
         const driver = row.original as Driver;
         return (
            <div className='flex justify-center gap-1'>
               <button
                  className='text-sm text-gray-600 hover:text-gray-900 border border-gray-300 hover:border-gray-400 px-3 py-1 rounded transition-colors bg-white'
                  onClick={() => handleViewClick(driver.id)}
               >
                  View
               </button>
               <button
                  className='text-sm text-gray-600 hover:text-blue-600 border border-gray-300 hover:border-blue-400 px-3 py-1 rounded transition-colors bg-white'
                  onClick={() => handleEditClick(driver.id)}
               >
                  Edit
               </button>
               <button
                  className='text-sm text-gray-600 hover:text-red-600 border border-gray-300 hover:border-red-400 px-3 py-1 rounded transition-colors bg-white'
                  onClick={() => handleDeleteClick(driver.id)}
                  disabled={deleteDriverMutation.isPending && driverToDelete === driver.id}
               >
                  Delete
               </button>
            </div>
         );
      },
      size: 200,
   },
];

interface DriverTableProps {
   data: ListDriverResponse | undefined;
   isLoading: boolean;
   currentPage: number;
   onPageChange: (page: number) => void;
   hasFilters: boolean;
   hasSearch: boolean;
   hasStatus: boolean;
   hasLocation: boolean;
   onClearFilters: () => void;
}

export function DriverTable({
   data,
   isLoading,
   currentPage,
   onPageChange,
   hasFilters,
   hasSearch,
   hasStatus,
   hasLocation,
   onClearFilters,
}: DriverTableProps) {
   const [driverToDelete, setDriverToDelete] = useState<number | null>(null);
   const [driverToEdit, setDriverToEdit] = useState<number | null>(null);
   const deleteDriverMutation = useDeleteDriver();
   const queryClient = useQueryClient();

   const handleDeleteClick = (id: number) => {
      setDriverToDelete(id);
   };

   const handleEditClick = (id: number) => {
      setDriverToEdit(id);
   };

   const handleViewClick = (id: number) => {
      // Handle view logic - could navigate to a detail page
      console.log('View driver:', id);
   };

   const handleDeleteConfirm = () => {
      if (!driverToDelete) return;

      deleteDriverMutation.mutate(driverToDelete, {
         onSuccess: () => {
            toast.success('Driver deleted successfully');
            setDriverToDelete(null);
            queryClient.invalidateQueries({ queryKey: ['driver'] });
         },
         onError: error => {
            toast.error(error);
            setDriverToDelete(null);
         },
      });
   };

   const columns = getColumns({
      deleteDriverMutation,
      handleDeleteClick,
      handleEditClick,
      handleViewClick,
      driverToDelete,
   });

   const table = useReactTable({
      data: data?.data || [],
      columns,
      getCoreRowModel: getCoreRowModel(),
   });

   // Loading state
   if (isLoading) {
      return <DriverTableLoading />;
   }

   // Empty state with filters
   if (!data?.data?.length && hasFilters) {
      return (
         <DriverTableFilteredEmpty
            hasSearch={hasSearch}
            hasStatus={hasStatus}
            hasLocation={hasLocation}
            onClearFilters={onClearFilters}
         />
      );
   }

   // Empty state without filters
   if (!data?.data?.length) {
      return <DriverTableEmpty />;
   }

   return (
      <div className='space-y-4'>
         <div className='rounded-md border'>
            <div className='overflow-x-auto'>
               <table className='w-full'>
                  <thead>
                     {table.getHeaderGroups().map(headerGroup => (
                        <tr key={headerGroup.id} className='border-b bg-gray-50'>
                           {headerGroup.headers.map(header => (
                              <th
                                 key={header.id}
                                 className='h-11 px-4 text-left align-middle'
                                 style={{ width: header.getSize() }}
                              >
                                 {header.isPlaceholder
                                    ? null
                                    : flexRender(
                                         header.column.columnDef.header,
                                         header.getContext()
                                      )}
                              </th>
                           ))}
                        </tr>
                     ))}
                  </thead>
                  <tbody>
                     {table.getRowModel().rows.map(row => (
                        <tr key={row.id} className='border-b transition-colors hover:bg-gray-50/30'>
                           {row.getVisibleCells().map(cell => (
                              <td key={cell.id} className='px-4 py-3 align-middle'>
                                 {flexRender(cell.column.columnDef.cell, cell.getContext())}
                              </td>
                           ))}
                        </tr>
                     ))}
                  </tbody>
               </table>
            </div>
         </div>

         {/* Pagination */}
         {data && data.totalPages > 1 && (
            <CustomPagination
               currentPage={currentPage}
               totalPages={data.totalPages}
               onPageChange={onPageChange}
               hasNext={false}
               hasPrev={false}
            />
         )}

         {/* Delete Confirmation Dialog */}
         <DeleteDriverDialog
            isOpen={!!driverToDelete}
            onClose={() => setDriverToDelete(null)}
            onConfirm={handleDeleteConfirm}
            isLoading={deleteDriverMutation.isPending}
         />

         {/* Edit Driver Sheet */}
         <EditDriver
            driverId={driverToEdit}
            isOpen={!!driverToEdit}
            onClose={() => setDriverToEdit(null)}
         />
      </div>
   );
}
