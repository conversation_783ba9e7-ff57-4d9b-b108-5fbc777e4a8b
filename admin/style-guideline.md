# Tukxi Admin Style Guidelines

## Overview
This document establishes the design principles and aesthetic guidelines for the Tukxi Admin dashboard, based on the clean, simple, and minimalistic UI approach that is pleasing to the eye.

## Design Philosophy

### Core Principles
1. **Clean & Minimalistic**: Remove unnecessary visual clutter and focus on essential elements
2. **Simple & Intuitive**: Prioritize user experience with clear navigation and obvious interactions
3. **Consistent**: Maintain uniformity across all components and pages
4. **Accessible**: Ensure readability and usability for all users
5. **Professional**: Maintain a business-appropriate aesthetic

## Color Palette

### Primary Colors
- **Primary Blue**: `#4361EE` (oklch(0.588 0.243 264.376))
  - Used for: Primary buttons, links, active states, brand elements
  - Conveys: Trust, professionalism, action

### Neutral Colors
- **White**: `#FFFFFF` - Background, cards, clean surfaces
- **Light Gray**: `#F3F3F3` - Secondary backgrounds, subtle borders
- **Medium Gray**: `#7D7D7D` - Secondary text, placeholders
- **Dark Gray**: `#2D2D2D` - Primary text, headings
- **Border Gray**: `#E8E8E8` - Subtle borders, dividers

### Status Colors
- **Success Green**: `#059669` (text-green-600) - Active states, positive actions
- **Warning Yellow**: `#D97706` (text-yellow-600) - Pending states, caution
- **Error Red**: `#DC2626` (text-red-600) - Inactive states, destructive actions
- **Info Blue**: `#2563EB` (text-blue-600) - Information, neutral actions

## Typography

### Font Family
- **Primary**: Geist Sans (system fallback: -apple-system, BlinkMacSystemFont, "Segoe UI")
- **Monospace**: Geist Mono (for code, IDs, technical data)

### Font Weights
- **Regular (400)**: Body text, descriptions
- **Medium (500)**: Subheadings, labels
- **Semibold (600)**: Headings, important text
- **Bold (700)**: Page titles, emphasis

### Font Sizes
- **Display**: 32px - Page titles
- **Heading 1**: 24px - Section headers
- **Heading 2**: 20px - Subsection headers
- **Heading 3**: 18px - Component headers
- **Body**: 16px - Regular text
- **Small**: 14px - Secondary text, captions
- **Tiny**: 12px - Labels, metadata

## Layout & Spacing

### Grid System
- **Container**: Max-width with centered content
- **Columns**: Flexible grid based on content needs
- **Gutters**: 16px between columns

### Spacing Scale (Tailwind-based)
- **xs**: 4px - Tight spacing
- **sm**: 8px - Close elements
- **md**: 16px - Standard spacing
- **lg**: 24px - Section spacing
- **xl**: 32px - Large gaps
- **2xl**: 48px - Major sections

### Layout Principles
1. **Single Input Per Row**: Forms should have one input field per row at 100% width
2. **Consistent Margins**: Use standard spacing scale throughout
3. **Visual Hierarchy**: Use whitespace to create clear content groupings
4. **Responsive Design**: Ensure layouts work on all screen sizes

## Components

### Buttons
- **Primary**: Blue background (#4361EE), white text, rounded corners
- **Secondary**: White background, blue border, blue text
- **Text Buttons**: No background, colored text only, minimal padding
- **Ghost**: Transparent background, colored text only
- **Sizes**: Small (32px), Medium (40px), Large (48px)

### Action Buttons (Tables)
- **Style**: Simple text buttons with subtle rounded corners and padding (px-3 py-1 rounded)
- **Default State**: Gray text (text-gray-600) with no background
- **Hover Effects**:
  - View: Darker gray text (hover:text-gray-900) with light gray background (hover:bg-gray-50)
  - Edit: Blue text (hover:text-blue-600) with light blue background (hover:bg-blue-50)
  - Delete: Red text (hover:text-red-600) with light red background (hover:bg-red-50)
- **Transitions**: Smooth color transitions (transition-colors)
- **No Icons**: Keep actions clean with text only
- **Font Size**: Small text (text-sm) for compact appearance

### Form Elements
- **Inputs**: Full width, consistent height (40px), subtle border
- **Labels**: Above inputs, medium weight, clear hierarchy
- **Validation**: Inline error messages, red color for errors
- **Placeholders**: Light gray text, helpful but not overwhelming

### Cards
- **Background**: White with subtle shadow
- **Border**: Light gray, minimal
- **Padding**: Consistent internal spacing (16-24px)
- **Radius**: Subtle rounded corners (8px)

### Tables
- **Headers**: Semibold weight (font-semibold), greyish color (text-gray-600), smaller font size (text-sm), light gray background (bg-gray-50)
- **Rows**: Clean white background with subtle hover effect (hover:bg-gray-50/30)
- **Borders**: Minimal, light gray borders between rows
- **Cell Padding**: Reduced padding (px-4 py-3) for compact appearance
- **Font Sizes**: Smaller overall - text-sm for main content, text-xs for secondary info
- **Actions**: Simple button styling with hover effects, center-aligned
- **Data Emphasis**: Only primary data (like names) should be bold/semibold, secondary data should be regular weight
- **Clean Design**: Remove unnecessary elements like refresh buttons and row count displays
- **Focus on Content**: Prioritize data visibility over interface elements

### Navigation
- **Sidebar**: Clean, minimal icons with text
- **Breadcrumbs**: Clear hierarchy, subtle separators
- **Active States**: Clear visual indication using primary color

## Interaction States

### Hover States
- **Buttons**: Slightly darker background
- **Links**: Underline or color change
- **Cards**: Subtle shadow increase
- **Table Rows**: Light background highlight

### Focus States
- **All Interactive Elements**: Blue outline ring
- **Keyboard Navigation**: Clear focus indicators
- **Form Fields**: Blue border highlight

### Loading States
- **Buttons**: Disabled state with spinner
- **Tables**: Skeleton loading patterns
- **Forms**: Disabled inputs during submission

## Data Visualization

### Status Indicators
- **Text Only**: Simple colored text without backgrounds or borders
- **Colors**: Use status colors directly on text (green, yellow, red)
- **No Icons**: Keep status indicators clean with text only
- **Progress**: Display as simple percentage text

### Info Cards
- **Layout**: Horizontal layout with label and value side by side
- **Background**: Light gray background (bg-gray-50) with subtle border
- **Height**: Match button height for consistency
- **Content**: Minimal text, clear hierarchy

## Best Practices

### Do's
- ✅ Use consistent spacing throughout the application
- ✅ Maintain single input per row in forms
- ✅ Use the established color palette
- ✅ Keep interfaces clean and uncluttered
- ✅ Use simple text for status indicators and actions
- ✅ Avoid unnecessary icons and decorative elements
- ✅ Use appropriate font weights for hierarchy
- ✅ Use smaller font sizes (text-sm, text-xs) for compact, readable tables
- ✅ Apply subtle hover effects on interactive elements
- ✅ Keep table headers bold and greyish for clear hierarchy
- ✅ Remove unnecessary UI elements like refresh buttons and counters

### Don'ts
- ❌ Don't use multiple inputs per row in forms
- ❌ Don't add unnecessary icons or badges
- ❌ Don't use complex backgrounds or borders for simple elements
- ❌ Don't overcrowd interfaces with unnecessary elements
- ❌ Don't use inconsistent spacing or sizing
- ❌ Don't ignore accessibility considerations
- ❌ Don't break established patterns without good reason
- ❌ Don't make all table data bold - reserve bold text for primary identifiers only
- ❌ Don't add refresh buttons or row counters unless absolutely necessary
- ❌ Don't use large font sizes in tables - keep them compact and scannable

## Implementation Notes

### CSS Variables
All colors should be defined using CSS custom properties in `globals.css` for easy theming and consistency.

### Component Library
Use shadcn/ui components as the foundation, customized to match these guidelines.

### Responsive Breakpoints
- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

## Future Considerations

### Dark Mode
When implementing dark mode, maintain the same principles with appropriate color inversions while preserving contrast ratios.

### Accessibility
Ensure all color combinations meet WCAG 2.1 AA standards for contrast ratios.

### Scalability
These guidelines should evolve with the product while maintaining core principles of simplicity and clarity.

---

*This style guide should be referenced for all new features and updated as the design system evolves.*
